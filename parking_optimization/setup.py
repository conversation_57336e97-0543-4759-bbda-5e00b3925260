"""
Setup script for parking lot optimization system.

This script helps users set up and verify the system installation.
"""

import os
import sys
import subprocess
import importlib


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required.")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    else:
        print(f"✓ Python version {version.major}.{version.minor}.{version.micro} is compatible.")
        return True


def install_dependencies():
    """Install required dependencies."""
    print("\nInstalling dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def check_dependencies():
    """Check if all required dependencies are available."""
    print("\nChecking dependencies...")
    
    required_packages = [
        "pyqubo", "numpy", "matplotlib", "shapely", 
        "scipy", "networkx", "sklearn", "pandas", "seaborn"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "sklearn":
                importlib.import_module("sklearn")
            else:
                importlib.import_module(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        return False
    else:
        print("\n✓ All dependencies are available.")
        return True


def run_system_test():
    """Run system tests to verify installation."""
    print("\nRunning system tests...")
    
    try:
        # Import test module
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from test_system import SystemTester
        
        # Run tests
        tester = SystemTester()
        tester.run_all_tests()
        
        # Check if all tests passed
        passed = sum(1 for _, result in tester.test_results if result == "PASSED")
        total = len(tester.test_results)
        
        return passed == total
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False


def create_sample_output():
    """Create sample output to verify system works."""
    print("\nCreating sample output...")
    
    try:
        # Run a quick demo
        from main import run_single_scenario_demo
        
        # Redirect output to suppress plots
        import matplotlib
        matplotlib.use('Agg')
        
        # Run demo on simplest scenario
        run_single_scenario_demo("rectangular_simple")
        
        print("✓ Sample output created successfully.")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create sample output: {e}")
        return False


def print_usage_instructions():
    """Print usage instructions."""
    print("\n" + "=" * 60)
    print("SETUP COMPLETE - USAGE INSTRUCTIONS")
    print("=" * 60)
    print()
    print("The parking lot optimization system is ready to use!")
    print()
    print("Quick start options:")
    print("  python run_demo.py           - Interactive demo")
    print("  python main.py demo          - Single scenario demo")
    print("  python main.py               - Full optimization demo")
    print("  python example_usage.py      - Example usage patterns")
    print("  python test_system.py        - Run system tests")
    print()
    print("Available test scenarios:")
    print("  - rectangular_simple         - Simple rectangular lot")
    print("  - rectangular_with_obstacles - Rectangular with obstacles")
    print("  - l_shaped_lot              - L-shaped irregular lot")
    print("  - irregular_complex         - Complex irregular shape")
    print("  - large_commercial          - Large commercial lot")
    print()
    print("For detailed documentation, see README.md")
    print()


def main():
    """Main setup function."""
    print("PARKING LOT OPTIMIZATION SYSTEM SETUP")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if dependencies are already installed
    deps_available = check_dependencies()
    
    if not deps_available:
        # Try to install dependencies
        install_success = install_dependencies()
        if not install_success:
            print("\n❌ Setup failed. Please install dependencies manually:")
            print("   pip install -r requirements.txt")
            sys.exit(1)
        
        # Check again after installation
        if not check_dependencies():
            print("\n❌ Dependencies still missing after installation.")
            sys.exit(1)
    
    # Run system tests
    test_success = run_system_test()
    
    if test_success:
        print("\n🎉 Setup completed successfully!")
        print_usage_instructions()
    else:
        print("\n❌ Setup completed but some tests failed.")
        print("   The system may still work, but please check the test output above.")
        print_usage_instructions()


if __name__ == "__main__":
    main()
