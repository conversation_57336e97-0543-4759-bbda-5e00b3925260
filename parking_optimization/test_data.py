"""
Test data generation module for parking lot optimization.

This module generates various test scenarios with different parking lot shapes,
obstacles, and entrance configurations for testing the optimization system.
"""

import numpy as np
import random
from shapely.geometry import <PERSON>ygon, Point
from typing import List, Tuple

from .geometry import ParkingLot, Obstacle


class TestDataGenerator:
    """Generates test data for parking lot optimization scenarios."""
    
    def __init__(self, seed: int = 42):
        """Initialize with random seed for reproducible results."""
        random.seed(seed)
        np.random.seed(seed)
    
    def generate_all_test_scenarios(self) -> List[Tuple[str, ParkingLot]]:
        """Generate all test scenarios."""
        scenarios = [
            ("rectangular_simple", self.generate_rectangular_lot()),
            ("rectangular_with_obstacles", self.generate_rectangular_lot_with_obstacles()),
            ("l_shaped_lot", self.generate_l_shaped_lot()),
            ("irregular_complex", self.generate_irregular_lot()),
            ("large_commercial", self.generate_large_commercial_lot())
        ]
        
        return scenarios
    
    def generate_rectangular_lot(self) -> ParkingLot:
        """Generate a simple rectangular parking lot."""
        # Create rectangular boundary (50m x 30m)
        boundary = Polygon([
            (0, 0), (50, 0), (50, 30), (0, 30), (0, 0)
        ])
        
        lot = ParkingLot(boundary)
        
        # Add entrance on the bottom edge
        lot.add_entrance(edge_id=0, position_ratio=0.3)
        
        return lot
    
    def generate_rectangular_lot_with_obstacles(self) -> ParkingLot:
        """Generate rectangular lot with random obstacles."""
        # Create rectangular boundary (60m x 40m)
        boundary = Polygon([
            (0, 0), (60, 0), (60, 40), (0, 40), (0, 0)
        ])
        
        lot = ParkingLot(boundary)
        
        # Add entrance
        lot.add_entrance(edge_id=0, position_ratio=0.2)
        
        # Add random obstacles
        obstacles = self._generate_random_obstacles(boundary, num_obstacles=3)
        for obstacle in obstacles:
            lot.add_obstacle(obstacle)
        
        return lot
    
    def generate_l_shaped_lot(self) -> ParkingLot:
        """Generate an L-shaped parking lot."""
        # Create L-shaped boundary
        boundary = Polygon([
            (0, 0), (40, 0), (40, 20), (20, 20), (20, 40), (0, 40), (0, 0)
        ])
        
        lot = ParkingLot(boundary)
        
        # Add entrances on different edges
        lot.add_entrance(edge_id=0, position_ratio=0.5)  # Bottom edge
        lot.add_entrance(edge_id=4, position_ratio=0.3)  # Left edge of vertical part
        
        # Add a few obstacles
        obstacles = [
            Obstacle(Polygon([(15, 15), (18, 15), (18, 18), (15, 18)]), "utility_box"),
            Obstacle(Polygon([(5, 25), (8, 25), (8, 35), (5, 35)]), "building")
        ]
        
        for obstacle in obstacles:
            lot.add_obstacle(obstacle)
        
        return lot
    
    def generate_irregular_lot(self) -> ParkingLot:
        """Generate an irregular shaped parking lot."""
        # Create irregular boundary
        boundary = Polygon([
            (0, 0), (35, 5), (45, 15), (50, 30), (40, 45), 
            (25, 40), (10, 35), (5, 20), (0, 0)
        ])
        
        lot = ParkingLot(boundary)
        
        # Add multiple entrances
        lot.add_entrance(edge_id=0, position_ratio=0.4)
        lot.add_entrance(edge_id=3, position_ratio=0.6)
        
        # Add various obstacles
        obstacles = [
            Obstacle(Polygon([(20, 20), (25, 20), (25, 25), (20, 25)]), "building"),
            Obstacle(Polygon([(35, 10), (37, 10), (37, 12), (35, 12)]), "tree"),
            Obstacle(Polygon([(15, 30), (18, 30), (18, 33), (15, 33)]), "utility"),
            Obstacle(Polygon([(30, 35), (33, 35), (33, 38), (30, 38)]), "tree")
        ]
        
        for obstacle in obstacles:
            lot.add_obstacle(obstacle)
        
        return lot
    
    def generate_large_commercial_lot(self) -> ParkingLot:
        """Generate a large commercial parking lot."""
        # Create large rectangular boundary (100m x 80m)
        boundary = Polygon([
            (0, 0), (100, 0), (100, 80), (0, 80), (0, 0)
        ])
        
        lot = ParkingLot(boundary)
        
        # Add multiple entrances
        lot.add_entrance(edge_id=0, position_ratio=0.2)  # Bottom left
        lot.add_entrance(edge_id=0, position_ratio=0.8)  # Bottom right
        lot.add_entrance(edge_id=2, position_ratio=0.5)  # Top center
        
        # Add larger obstacles representing buildings and landscaping
        obstacles = [
            # Central building
            Obstacle(Polygon([(40, 30), (60, 30), (60, 50), (40, 50)]), "building"),
            # Landscaping islands
            Obstacle(Polygon([(20, 20), (25, 20), (25, 25), (20, 25)]), "landscaping"),
            Obstacle(Polygon([(75, 20), (80, 20), (80, 25), (75, 25)]), "landscaping"),
            Obstacle(Polygon([(20, 55), (25, 55), (25, 60), (20, 60)]), "landscaping"),
            Obstacle(Polygon([(75, 55), (80, 55), (80, 60), (75, 60)]), "landscaping"),
            # Utility structures
            Obstacle(Polygon([(10, 10), (15, 10), (15, 15), (10, 15)]), "utility"),
            Obstacle(Polygon([(85, 65), (90, 65), (90, 70), (85, 70)]), "utility")
        ]
        
        for obstacle in obstacles:
            lot.add_obstacle(obstacle)
        
        return lot
    
    def _generate_random_obstacles(self, boundary: Polygon, num_obstacles: int = 3) -> List[Obstacle]:
        """Generate random obstacles within the boundary."""
        obstacles = []
        bounds = boundary.bounds
        
        for i in range(num_obstacles):
            # Random obstacle size
            width = random.uniform(3, 8)
            height = random.uniform(3, 8)
            
            # Random position (ensure it's inside boundary with some margin)
            margin = 5
            x = random.uniform(bounds[0] + margin, bounds[2] - margin - width)
            y = random.uniform(bounds[1] + margin, bounds[3] - margin - height)
            
            # Create obstacle polygon
            obstacle_poly = Polygon([
                (x, y), (x + width, y), (x + width, y + height), (x, y + height), (x, y)
            ])
            
            # Ensure obstacle is within boundary
            if boundary.contains(obstacle_poly):
                obstacle_type = random.choice(["building", "tree", "utility", "landscaping"])
                obstacles.append(Obstacle(obstacle_poly, obstacle_type))
        
        return obstacles
    
    def generate_custom_lot(self, vertices: List[Tuple[float, float]], 
                          entrance_specs: List[Tuple[int, float]] = None,
                          obstacle_specs: List[Tuple[List[Tuple[float, float]], str]] = None) -> ParkingLot:
        """Generate a custom parking lot with specified parameters."""
        # Create boundary from vertices
        boundary = Polygon(vertices)
        lot = ParkingLot(boundary)
        
        # Add entrances if specified
        if entrance_specs:
            for edge_id, position_ratio in entrance_specs:
                lot.add_entrance(edge_id, position_ratio)
        
        # Add obstacles if specified
        if obstacle_specs:
            for obstacle_vertices, obstacle_type in obstacle_specs:
                obstacle_poly = Polygon(obstacle_vertices)
                obstacle = Obstacle(obstacle_poly, obstacle_type)
                lot.add_obstacle(obstacle)
        
        return lot
    
    def get_test_scenario_info(self) -> List[Dict]:
        """Get information about all test scenarios."""
        scenarios_info = [
            {
                "name": "rectangular_simple",
                "description": "Simple 50x30m rectangular lot with single entrance",
                "complexity": "Low",
                "features": ["Single entrance", "No obstacles", "Regular shape"]
            },
            {
                "name": "rectangular_with_obstacles", 
                "description": "60x40m rectangular lot with random obstacles",
                "complexity": "Medium",
                "features": ["Single entrance", "Multiple obstacles", "Regular shape"]
            },
            {
                "name": "l_shaped_lot",
                "description": "L-shaped lot with two entrances and obstacles",
                "complexity": "Medium",
                "features": ["Multiple entrances", "Some obstacles", "Irregular shape"]
            },
            {
                "name": "irregular_complex",
                "description": "Irregular shaped lot with multiple entrances and obstacles",
                "complexity": "High", 
                "features": ["Multiple entrances", "Many obstacles", "Complex irregular shape"]
            },
            {
                "name": "large_commercial",
                "description": "Large 100x80m commercial lot with central building",
                "complexity": "High",
                "features": ["Multiple entrances", "Large central obstacle", "Commercial scale"]
            }
        ]
        
        return scenarios_info
