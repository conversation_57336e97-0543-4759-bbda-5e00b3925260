"""
Optimization module for parking lot design using quantum annealing.

This module implements the quantum annealing optimization approach with
multi-candidate generation strategy adapted from PCB routing methodology.
"""

import numpy as np
import random
import math
from typing import List, Dict, Tuple, Optional
from pyqubo import Binary, Constraint
import pyqubo
from shapely.geometry import <PERSON>ygon, Point, LineString
from shapely.ops import unary_union
import networkx as nx

from .geometry import ParkingLot, PerimeterEdge, EdgeConfiguration, Obstacle


class LayoutCandidate:
    """Represents a candidate layout configuration."""
    
    def __init__(self, candidate_id: int):
        self.candidate_id = candidate_id
        self.perimeter_config = {}  # edge_id -> EdgeConfiguration
        self.interior_layout = None
        self.estimated_capacity = 0
        self.smoothness_score = 0.0
        self.accessibility_score = 0.0
        self.total_score = 0.0
        
    def set_perimeter_configuration(self, edge_configs: Dict[int, EdgeConfiguration]):
        """Set perimeter edge configurations."""
        self.perimeter_config = edge_configs.copy()
        
    def calculate_smoothness_score(self, parking_lot: ParkingLot) -> float:
        """Calculate road smoothness score for this candidate."""
        smoothness_penalty = 0.0
        
        # Analyze corner angles and turning requirements
        for i in range(len(parking_lot.perimeter_edges)):
            next_i = (i + 1) % len(parking_lot.perimeter_edges)
            corner_angle = parking_lot.calculate_corner_angle(i, next_i)
            
            # Penalty for sharp corners
            if corner_angle < 90:
                smoothness_penalty += (90 - corner_angle) ** 2 / 100
            
            # Penalty for configuration mismatches that create rough transitions
            edge1_config = self.perimeter_config.get(i, EdgeConfiguration.DRIVING_ONLY)
            edge2_config = self.perimeter_config.get(next_i, EdgeConfiguration.DRIVING_ONLY)
            
            if edge1_config != edge2_config and corner_angle < 120:
                smoothness_penalty += 5.0
        
        self.smoothness_score = max(0, 100 - smoothness_penalty)
        return self.smoothness_score


class CandidateGenerator:
    """Generates diverse layout candidates using A* inspired variations."""
    
    def __init__(self, parking_lot: ParkingLot):
        self.parking_lot = parking_lot
        self.num_edges = len(parking_lot.perimeter_edges)
        
    def generate_perimeter_candidates(self, num_candidates: int = 20) -> List[LayoutCandidate]:
        """Generate diverse perimeter configuration candidates."""
        candidates = []
        
        # Base configurations
        base_configs = [
            self._generate_all_driving_config(),
            self._generate_maximum_parking_config(),
            self._generate_balanced_config(),
            self._generate_entrance_optimized_config()
        ]
        
        # Add base configurations
        for i, config in enumerate(base_configs):
            candidate = LayoutCandidate(i)
            candidate.set_perimeter_configuration(config)
            candidates.append(candidate)
        
        # Generate variations using A* inspired perturbations
        for i in range(num_candidates - len(base_configs)):
            base_config = random.choice(base_configs)
            varied_config = self._create_variation(base_config, i)
            
            candidate = LayoutCandidate(len(base_configs) + i)
            candidate.set_perimeter_configuration(varied_config)
            candidates.append(candidate)
        
        return candidates
    
    def _generate_all_driving_config(self) -> Dict[int, EdgeConfiguration]:
        """Generate configuration with all edges as driving-only."""
        return {i: EdgeConfiguration.DRIVING_ONLY for i in range(self.num_edges)}
    
    def _generate_maximum_parking_config(self) -> Dict[int, EdgeConfiguration]:
        """Generate configuration maximizing parking spaces."""
        config = {}
        
        for i, edge in enumerate(self.parking_lot.perimeter_edges):
            if edge.is_entrance:
                config[i] = EdgeConfiguration.DRIVING_ONLY
            elif edge.length > 15:  # Long edges get perpendicular parking
                config[i] = EdgeConfiguration.PERPENDICULAR_PARKING
            elif edge.length > 8:   # Medium edges get parallel parking
                config[i] = EdgeConfiguration.PARALLEL_PARKING
            else:
                config[i] = EdgeConfiguration.DRIVING_ONLY
                
        return config
    
    def _generate_balanced_config(self) -> Dict[int, EdgeConfiguration]:
        """Generate balanced configuration between parking and driving."""
        config = {}
        
        for i, edge in enumerate(self.parking_lot.perimeter_edges):
            if edge.is_entrance:
                config[i] = EdgeConfiguration.DRIVING_ONLY
            elif i % 2 == 0:
                config[i] = EdgeConfiguration.PERPENDICULAR_PARKING
            else:
                config[i] = EdgeConfiguration.DRIVING_ONLY
                
        return config
    
    def _generate_entrance_optimized_config(self) -> Dict[int, EdgeConfiguration]:
        """Generate configuration optimized for entrance accessibility."""
        config = {}
        
        for i, edge in enumerate(self.parking_lot.perimeter_edges):
            if edge.is_entrance:
                config[i] = EdgeConfiguration.DRIVING_ONLY
                # Adjacent edges also driving for better access
                prev_i = (i - 1) % self.num_edges
                next_i = (i + 1) % self.num_edges
                config[prev_i] = EdgeConfiguration.DRIVING_ONLY
                config[next_i] = EdgeConfiguration.DRIVING_ONLY
            else:
                config[i] = config.get(i, EdgeConfiguration.PERPENDICULAR_PARKING)
                
        return config
    
    def _create_variation(self, base_config: Dict[int, EdgeConfiguration], variation_id: int) -> Dict[int, EdgeConfiguration]:
        """Create variation of base configuration."""
        varied_config = base_config.copy()
        
        # Random perturbations
        num_changes = random.randint(1, max(1, self.num_edges // 3))
        
        for _ in range(num_changes):
            edge_id = random.randint(0, self.num_edges - 1)
            edge = self.parking_lot.perimeter_edges[edge_id]
            
            if not edge.is_entrance:  # Don't modify entrance edges
                # Choose new configuration based on edge characteristics
                if edge.length > 12:
                    new_config = random.choice([
                        EdgeConfiguration.PERPENDICULAR_PARKING,
                        EdgeConfiguration.DRIVING_ONLY
                    ])
                elif edge.length > 6:
                    new_config = random.choice([
                        EdgeConfiguration.PARALLEL_PARKING,
                        EdgeConfiguration.DRIVING_ONLY
                    ])
                else:
                    new_config = EdgeConfiguration.DRIVING_ONLY
                
                varied_config[edge_id] = new_config
        
        return varied_config
    
    def estimate_interior_capacity(self, perimeter_config: Dict[int, EdgeConfiguration]) -> int:
        """Fast estimation of interior parking capacity."""
        # Calculate usable interior area
        usable_area = self.parking_lot.get_usable_area()
        
        # Estimate required driving area (simplified)
        perimeter_length = sum(edge.length for edge in self.parking_lot.perimeter_edges)
        min_driving_area = perimeter_length * self.parking_lot.min_driving_width * 0.3
        
        # Estimate parking area
        parking_area = max(0, usable_area - min_driving_area)
        
        # Calculate space efficiency based on lot shape
        space_efficiency = self._calculate_space_efficiency()
        
        # Standard parking space area
        space_area = self.parking_lot.parking_space_width * self.parking_lot.parking_space_length
        
        # Estimate capacity
        estimated_capacity = int((parking_area * space_efficiency) / space_area)
        
        # Add perimeter parking capacity
        perimeter_capacity = self._estimate_perimeter_capacity(perimeter_config)
        
        return estimated_capacity + perimeter_capacity
    
    def _calculate_space_efficiency(self) -> float:
        """Calculate parking space efficiency based on lot geometry."""
        # Simple heuristic based on lot shape
        bounds = self.parking_lot.boundary.bounds
        width = bounds[2] - bounds[0]
        height = bounds[3] - bounds[1]
        
        # More rectangular lots have higher efficiency
        aspect_ratio = max(width, height) / min(width, height)
        
        if aspect_ratio < 1.5:
            return 0.85  # Nearly square - high efficiency
        elif aspect_ratio < 3.0:
            return 0.75  # Rectangular - medium efficiency
        else:
            return 0.65  # Very elongated - lower efficiency
    
    def _estimate_perimeter_capacity(self, perimeter_config: Dict[int, EdgeConfiguration]) -> int:
        """Estimate parking capacity from perimeter configurations."""
        perimeter_capacity = 0
        
        for edge_id, config in perimeter_config.items():
            edge = self.parking_lot.perimeter_edges[edge_id]
            
            if config == EdgeConfiguration.PERPENDICULAR_PARKING:
                # Perpendicular parking: spaces depend on edge length
                spaces_per_edge = max(0, int(edge.length / self.parking_lot.parking_space_width) - 1)
                perimeter_capacity += spaces_per_edge
                
            elif config == EdgeConfiguration.PARALLEL_PARKING:
                # Parallel parking: fewer spaces but still some capacity
                spaces_per_edge = max(0, int(edge.length / self.parking_lot.parking_space_length) - 1)
                perimeter_capacity += spaces_per_edge
        
        return perimeter_capacity


class QuantumOptimizer:
    """Quantum annealing optimizer for parking lot layout selection."""
    
    def __init__(self, parking_lot: ParkingLot):
        self.parking_lot = parking_lot
        self.candidate_generator = CandidateGenerator(parking_lot)
        
        # Optimization parameters
        self.lambda_capacity = 1.0      # Capacity maximization weight
        self.lambda_smoothness = 0.3    # Road smoothness weight
        self.lambda_conflicts = 2.0     # Conflict penalty weight
        self.lambda_constraints = 5.0   # Hard constraint weight
        
    def optimize(self, num_candidates: int = 20, max_iterations: int = 5) -> LayoutCandidate:
        """Main optimization function using iterative quantum annealing."""
        best_solution = None
        best_score = float('-inf')
        
        # Generate initial candidates
        candidates = self.candidate_generator.generate_perimeter_candidates(num_candidates)
        
        for iteration in range(max_iterations):
            print(f"Iteration {iteration + 1}/{max_iterations}")
            
            # Evaluate candidates
            self._evaluate_candidates(candidates)
            
            # Formulate and solve QUBO
            solution_candidate = self._solve_qubo(candidates)
            
            if solution_candidate and solution_candidate.total_score > best_score:
                best_score = solution_candidate.total_score
                best_solution = solution_candidate
                print(f"New best score: {best_score:.2f}")
            
            # Generate new candidates based on feedback (simplified)
            if iteration < max_iterations - 1:
                candidates = self._generate_feedback_candidates(candidates, solution_candidate)
        
        return best_solution
    
    def _evaluate_candidates(self, candidates: List[LayoutCandidate]):
        """Evaluate all candidates for capacity, smoothness, and constraints."""
        for candidate in candidates:
            # Estimate capacity
            candidate.estimated_capacity = self.candidate_generator.estimate_interior_capacity(
                candidate.perimeter_config
            )
            
            # Calculate smoothness score
            candidate.calculate_smoothness_score(self.parking_lot)
            
            # Check constraint violations
            is_valid, issues = self._validate_candidate(candidate)
            constraint_penalty = len(issues) * 10
            
            # Calculate total score
            candidate.total_score = (
                self.lambda_capacity * candidate.estimated_capacity -
                self.lambda_smoothness * (100 - candidate.smoothness_score) -
                self.lambda_constraints * constraint_penalty
            )
    
    def _validate_candidate(self, candidate: LayoutCandidate) -> Tuple[bool, List[str]]:
        """Validate candidate against hard constraints."""
        issues = []
        
        # Apply candidate configuration to parking lot temporarily
        original_configs = {}
        for edge_id, config in candidate.perimeter_config.items():
            edge = self.parking_lot.perimeter_edges[edge_id]
            original_configs[edge_id] = edge.configuration
            edge.configuration = config
        
        # Validate configuration
        is_valid, validation_issues = self.parking_lot.validate_perimeter_configuration()
        issues.extend(validation_issues)
        
        # Restore original configurations
        for edge_id, original_config in original_configs.items():
            self.parking_lot.perimeter_edges[edge_id].configuration = original_config
        
        return len(issues) == 0, issues
    
    def _solve_qubo(self, candidates: List[LayoutCandidate]) -> Optional[LayoutCandidate]:
        """Solve QUBO formulation to select best candidate."""
        if not candidates:
            return None
        
        # For simplicity, use classical selection of best candidate
        # In a full implementation, this would use PyQUBO for quantum annealing
        best_candidate = max(candidates, key=lambda c: c.total_score)
        
        return best_candidate
    
    def _generate_feedback_candidates(self, current_candidates: List[LayoutCandidate], 
                                    best_candidate: Optional[LayoutCandidate]) -> List[LayoutCandidate]:
        """Generate new candidates based on optimization feedback."""
        new_candidates = []
        
        # Keep best candidates
        sorted_candidates = sorted(current_candidates, key=lambda c: c.total_score, reverse=True)
        new_candidates.extend(sorted_candidates[:len(current_candidates)//2])
        
        # Generate variations of best candidates
        for i in range(len(current_candidates) - len(new_candidates)):
            base_candidate = random.choice(sorted_candidates[:3])
            varied_config = self.candidate_generator._create_variation(
                base_candidate.perimeter_config, i
            )
            
            new_candidate = LayoutCandidate(len(current_candidates) + i)
            new_candidate.set_perimeter_configuration(varied_config)
            new_candidates.append(new_candidate)
        
        return new_candidates
