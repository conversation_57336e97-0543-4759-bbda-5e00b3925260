"""
Test script to verify the parking lot optimization system works correctly.

This script runs comprehensive tests on all components to ensure proper functionality.
"""

import sys
import os
import traceback
from typing import List, Tuple

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from geometry import ParkingLot, PerimeterEdge, Obstacle, EdgeConfiguration
    from optimization import QuantumOptimizer, CandidateGenerator, LayoutCandidate
    from visualization import ParkingLotVisualizer
    from test_data import TestDataGenerator
    from shapely.geometry import Polygon
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend for testing
    import matplotlib.pyplot as plt
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install required dependencies: pip install -r requirements.txt")
    sys.exit(1)


class SystemTester:
    """Comprehensive system testing class."""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
    
    def run_all_tests(self):
        """Run all system tests."""
        print("PARKING LOT OPTIMIZATION SYSTEM TESTS")
        print("=" * 50)
        print()
        
        tests = [
            ("Geometry Module", self.test_geometry_module),
            ("Test Data Generation", self.test_data_generation),
            ("Candidate Generation", self.test_candidate_generation),
            ("Optimization Engine", self.test_optimization_engine),
            ("Visualization System", self.test_visualization_system),
            ("Integration Test", self.test_integration),
        ]
        
        for test_name, test_func in tests:
            print(f"Running: {test_name}")
            try:
                test_func()
                self.test_results.append((test_name, "PASSED"))
                print(f"  ✓ {test_name} PASSED")
            except Exception as e:
                self.test_results.append((test_name, "FAILED"))
                self.failed_tests.append((test_name, str(e), traceback.format_exc()))
                print(f"  ✗ {test_name} FAILED: {e}")
            print()
        
        self.print_summary()
    
    def test_geometry_module(self):
        """Test geometry module functionality."""
        # Test ParkingLot creation
        boundary = Polygon([(0, 0), (50, 0), (50, 30), (0, 30)])
        lot = ParkingLot(boundary)
        
        assert len(lot.perimeter_edges) == 4, "Should have 4 perimeter edges"
        assert lot.boundary.area == 1500, "Area should be 1500"
        
        # Test entrance addition
        lot.add_entrance(edge_id=0, position_ratio=0.5)
        assert len(lot.entrances) == 1, "Should have 1 entrance"
        assert lot.perimeter_edges[0].is_entrance == True, "Edge 0 should be marked as entrance"
        
        # Test obstacle addition
        obstacle = Obstacle(Polygon([(10, 10), (15, 10), (15, 15), (10, 15)]), "test")
        lot.add_obstacle(obstacle)
        assert len(lot.obstacles) == 1, "Should have 1 obstacle"
        
        # Test edge configuration
        edge = lot.perimeter_edges[0]
        assert edge.length == 50, "Edge length should be 50"
        assert abs(edge.orientation) < 0.01, "Edge orientation should be ~0"
        
        # Test conflict detection
        lot.perimeter_edges[0].configuration = EdgeConfiguration.PERPENDICULAR_PARKING
        lot.perimeter_edges[1].configuration = EdgeConfiguration.PERPENDICULAR_PARKING
        conflicts = lot.detect_edge_conflicts(0, 1)
        # Should detect potential conflicts for perpendicular parking at corners
        
        print("    - ParkingLot creation: OK")
        print("    - Entrance/obstacle addition: OK")
        print("    - Edge calculations: OK")
        print("    - Conflict detection: OK")
    
    def test_data_generation(self):
        """Test test data generation."""
        generator = TestDataGenerator(seed=42)
        
        # Test all scenario generation
        scenarios = generator.generate_all_test_scenarios()
        assert len(scenarios) == 5, "Should generate 5 scenarios"
        
        scenario_names = [name for name, _ in scenarios]
        expected_names = ["rectangular_simple", "rectangular_with_obstacles", 
                         "l_shaped_lot", "irregular_complex", "large_commercial"]
        
        for expected in expected_names:
            assert expected in scenario_names, f"Missing scenario: {expected}"
        
        # Test individual scenarios
        for name, lot in scenarios:
            assert isinstance(lot, ParkingLot), f"Scenario {name} should return ParkingLot"
            assert lot.boundary.area > 0, f"Scenario {name} should have positive area"
            assert len(lot.entrances) > 0, f"Scenario {name} should have at least one entrance"
        
        # Test custom lot generation
        custom_lot = generator.generate_custom_lot(
            vertices=[(0, 0), (20, 0), (20, 15), (0, 15)],
            entrance_specs=[(0, 0.5)],
            obstacle_specs=[
                ([(5, 5), (10, 5), (10, 10), (5, 10)], "building")
            ]
        )
        
        assert custom_lot.boundary.area == 300, "Custom lot area should be 300"
        assert len(custom_lot.entrances) == 1, "Custom lot should have 1 entrance"
        assert len(custom_lot.obstacles) == 1, "Custom lot should have 1 obstacle"
        
        print("    - All scenarios generated: OK")
        print("    - Scenario validation: OK")
        print("    - Custom lot generation: OK")
    
    def test_candidate_generation(self):
        """Test candidate generation functionality."""
        # Create test lot
        boundary = Polygon([(0, 0), (40, 0), (40, 25), (0, 25)])
        lot = ParkingLot(boundary)
        lot.add_entrance(edge_id=0, position_ratio=0.3)
        
        # Test candidate generator
        generator = CandidateGenerator(lot)
        candidates = generator.generate_perimeter_candidates(20)
        
        assert len(candidates) == 20, "Should generate 20 candidates"
        
        # Check candidate properties
        for candidate in candidates:
            assert isinstance(candidate, LayoutCandidate), "Should be LayoutCandidate"
            assert len(candidate.perimeter_config) == 4, "Should have config for all 4 edges"
            
            # Check that entrance edge is properly configured
            entrance_edge_config = candidate.perimeter_config.get(0)
            # Entrance edges should typically be driving-only or allow access
        
        # Test capacity estimation
        test_config = {0: EdgeConfiguration.DRIVING_ONLY, 
                      1: EdgeConfiguration.PERPENDICULAR_PARKING,
                      2: EdgeConfiguration.DRIVING_ONLY, 
                      3: EdgeConfiguration.PERPENDICULAR_PARKING}
        
        capacity = generator.estimate_interior_capacity(test_config)
        assert capacity > 0, "Should estimate positive capacity"
        
        # Test base configurations
        all_driving = generator._generate_all_driving_config()
        assert all(config == EdgeConfiguration.DRIVING_ONLY for config in all_driving.values()), \
               "All driving config should have all edges as driving-only"
        
        print("    - Candidate generation: OK")
        print("    - Capacity estimation: OK")
        print("    - Base configurations: OK")
    
    def test_optimization_engine(self):
        """Test optimization engine functionality."""
        # Create test lot
        boundary = Polygon([(0, 0), (30, 0), (30, 20), (0, 20)])
        lot = ParkingLot(boundary)
        lot.add_entrance(edge_id=0, position_ratio=0.4)
        
        # Add obstacle
        obstacle = Obstacle(Polygon([(10, 8), (15, 8), (15, 12), (10, 12)]), "building")
        lot.add_obstacle(obstacle)
        
        # Test optimizer
        optimizer = QuantumOptimizer(lot)
        
        # Test with small parameters for speed
        solution = optimizer.optimize(num_candidates=8, max_iterations=2)
        
        assert isinstance(solution, LayoutCandidate), "Should return LayoutCandidate"
        assert solution.total_score > 0, "Should have positive total score"
        assert solution.estimated_capacity > 0, "Should have positive capacity"
        assert 0 <= solution.smoothness_score <= 100, "Smoothness score should be 0-100"
        
        # Test candidate evaluation
        candidates = optimizer.candidate_generator.generate_perimeter_candidates(5)
        optimizer._evaluate_candidates(candidates)
        
        for candidate in candidates:
            assert hasattr(candidate, 'total_score'), "Candidate should have total_score"
            assert hasattr(candidate, 'estimated_capacity'), "Candidate should have estimated_capacity"
            assert hasattr(candidate, 'smoothness_score'), "Candidate should have smoothness_score"
        
        print("    - Optimization execution: OK")
        print("    - Solution validation: OK")
        print("    - Candidate evaluation: OK")
    
    def test_visualization_system(self):
        """Test visualization system functionality."""
        # Create test scenario
        generator = TestDataGenerator(seed=42)
        scenarios = generator.generate_all_test_scenarios()
        lot = scenarios[0][1]  # Use first scenario
        
        # Create test solution
        optimizer = QuantumOptimizer(lot)
        solution = optimizer.optimize(num_candidates=5, max_iterations=1)
        candidates = optimizer.candidate_generator.generate_perimeter_candidates(5)
        optimizer._evaluate_candidates(candidates)
        
        # Test visualizer
        visualizer = ParkingLotVisualizer()
        
        # Test plot generation (don't show, just create)
        fig1 = visualizer.plot_original_lot(lot, "Test Original")
        assert fig1 is not None, "Should create original lot plot"
        plt.close(fig1)
        
        fig2 = visualizer.plot_optimized_layout(lot, solution, "Test Optimized")
        assert fig2 is not None, "Should create optimized layout plot"
        plt.close(fig2)
        
        fig3 = visualizer.plot_comparison(lot, candidates[:3], "Test Comparison")
        assert fig3 is not None, "Should create comparison plot"
        plt.close(fig3)
        
        fig4 = visualizer.plot_optimization_metrics(candidates)
        assert fig4 is not None, "Should create metrics plot"
        plt.close(fig4)
        
        print("    - Original lot plotting: OK")
        print("    - Optimized layout plotting: OK")
        print("    - Candidate comparison: OK")
        print("    - Metrics visualization: OK")
    
    def test_integration(self):
        """Test full system integration."""
        # Run a complete optimization workflow
        generator = TestDataGenerator(seed=42)
        scenarios = generator.generate_all_test_scenarios()
        
        # Test on rectangular simple scenario
        lot = scenarios[0][1]
        
        # Run optimization
        optimizer = QuantumOptimizer(lot)
        solution = optimizer.optimize(num_candidates=10, max_iterations=2)
        
        # Generate candidates for analysis
        candidates = optimizer.candidate_generator.generate_perimeter_candidates(10)
        optimizer._evaluate_candidates(candidates)
        
        # Test visualization
        visualizer = ParkingLotVisualizer()
        
        # Create all plots
        fig1 = visualizer.plot_original_lot(lot)
        fig2 = visualizer.plot_optimized_layout(lot, solution)
        fig3 = visualizer.plot_comparison(lot, candidates[:4])
        fig4 = visualizer.plot_optimization_metrics(candidates)
        
        # Close all figures
        for fig in [fig1, fig2, fig3, fig4]:
            plt.close(fig)
        
        # Validate results
        assert solution.total_score > 0, "Integration test should produce valid solution"
        assert len(candidates) == 10, "Integration test should generate correct number of candidates"
        
        # Test that best solution is reasonable
        sorted_candidates = sorted(candidates, key=lambda c: c.total_score, reverse=True)
        best_candidate = sorted_candidates[0]
        
        # Solution should be among top candidates
        solution_in_top = any(c.candidate_id == solution.candidate_id for c in sorted_candidates[:5])
        # Note: This might not always be true due to iterative improvement, so we'll just check it exists
        
        print("    - Full workflow execution: OK")
        print("    - Result validation: OK")
        print("    - Multi-component integration: OK")
    
    def print_summary(self):
        """Print test summary."""
        print("=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for _, result in self.test_results if result == "PASSED")
        total = len(self.test_results)
        
        print(f"Tests run: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print()
        
        if self.failed_tests:
            print("FAILED TESTS:")
            for test_name, error, traceback_str in self.failed_tests:
                print(f"  {test_name}: {error}")
                print(f"    {traceback_str.split('File')[-1].strip()}")
            print()
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! System is ready to use.")
        else:
            print("❌ Some tests failed. Please check the errors above.")
        
        print()
        print("To run the system:")
        print("  python main.py              - Full demo")
        print("  python main.py demo         - Single scenario demo")
        print("  python run_demo.py          - Quick demo")
        print("  python example_usage.py     - Example usage")


if __name__ == "__main__":
    tester = SystemTester()
    tester.run_all_tests()
