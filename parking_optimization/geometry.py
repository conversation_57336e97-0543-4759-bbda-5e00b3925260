"""
Geometry module for parking lot optimization.

This module contains classes and functions for handling geometric aspects
of parking lot design including perimeter edges, obstacles, and spatial calculations.
"""

import numpy as np
import math
from shapely.geometry import <PERSON>ygon, Point, LineString
from shapely.ops import unary_union
from typing import List, Tuple, Optional
from enum import Enum


class EdgeConfiguration(Enum):
    """Enumeration for perimeter edge configurations."""
    DRIVING_ONLY = "driving_only"
    PERPENDICULAR_PARKING = "perpendicular_parking"
    PARALLEL_PARKING = "parallel_parking"


class PerimeterEdge:
    """Represents a perimeter edge of the parking lot."""
    
    def __init__(self, start_point: Tuple[float, float], end_point: Tuple[float, float], edge_id: int):
        self.start_point = start_point
        self.end_point = end_point
        self.edge_id = edge_id
        self.length = self._calculate_length()
        self.orientation = self._calculate_orientation()
        self.configuration = None
        self.is_entrance = False
        
    def _calculate_length(self) -> float:
        """Calculate the length of the edge."""
        return math.sqrt((self.end_point[0] - self.start_point[0])**2 + 
                        (self.end_point[1] - self.start_point[1])**2)
    
    def _calculate_orientation(self) -> float:
        """Calculate the orientation angle of the edge in radians."""
        dx = self.end_point[0] - self.start_point[0]
        dy = self.end_point[1] - self.start_point[1]
        return math.atan2(dy, dx)
    
    def get_midpoint(self) -> Tuple[float, float]:
        """Get the midpoint of the edge."""
        return ((self.start_point[0] + self.end_point[0]) / 2,
                (self.start_point[1] + self.end_point[1]) / 2)
    
    def get_normal_vector(self) -> Tuple[float, float]:
        """Get the inward normal vector of the edge."""
        dx = self.end_point[0] - self.start_point[0]
        dy = self.end_point[1] - self.start_point[1]
        # Rotate 90 degrees counterclockwise for inward normal
        return (-dy / self.length, dx / self.length)


class Obstacle:
    """Represents an obstacle within the parking lot."""
    
    def __init__(self, polygon: Polygon, obstacle_type: str = "generic"):
        self.polygon = polygon
        self.obstacle_type = obstacle_type
        self.bounds = polygon.bounds
        
    def intersects_with(self, other_polygon: Polygon) -> bool:
        """Check if obstacle intersects with another polygon."""
        return self.polygon.intersects(other_polygon)
    
    def get_buffer_zone(self, buffer_distance: float) -> Polygon:
        """Get buffered zone around obstacle."""
        return self.polygon.buffer(buffer_distance)


class ParkingLot:
    """Main class representing a parking lot with all its components."""
    
    def __init__(self, boundary_polygon: Polygon):
        self.boundary = boundary_polygon
        self.perimeter_edges = self._create_perimeter_edges()
        self.obstacles = []
        self.entrances = []
        self.interior_region = None
        self.parking_spaces = []
        self.driving_lanes = []
        
        # Configuration parameters
        self.min_driving_width = 6.0  # meters
        self.parking_space_width = 2.5  # meters
        self.parking_space_length = 5.0  # meters
        self.min_turning_radius = 6.0  # meters
        
    def _create_perimeter_edges(self) -> List[PerimeterEdge]:
        """Create perimeter edges from boundary polygon."""
        coords = list(self.boundary.exterior.coords)[:-1]  # Remove duplicate last point
        edges = []
        
        for i in range(len(coords)):
            start_point = coords[i]
            end_point = coords[(i + 1) % len(coords)]
            edge = PerimeterEdge(start_point, end_point, i)
            edges.append(edge)
            
        return edges
    
    def add_obstacle(self, obstacle: Obstacle):
        """Add an obstacle to the parking lot."""
        self.obstacles.append(obstacle)
        self._update_interior_region()
    
    def add_entrance(self, edge_id: int, position_ratio: float = 0.5):
        """Add an entrance at specified position on perimeter edge."""
        if 0 <= edge_id < len(self.perimeter_edges):
            edge = self.perimeter_edges[edge_id]
            edge.is_entrance = True
            
            # Calculate entrance position
            x = edge.start_point[0] + position_ratio * (edge.end_point[0] - edge.start_point[0])
            y = edge.start_point[1] + position_ratio * (edge.end_point[1] - edge.start_point[1])
            
            self.entrances.append((x, y, edge_id))
    
    def _update_interior_region(self):
        """Update interior region by subtracting obstacles from boundary."""
        interior = self.boundary
        
        for obstacle in self.obstacles:
            # Add buffer zone around obstacles
            buffered_obstacle = obstacle.get_buffer_zone(1.0)
            interior = interior.difference(buffered_obstacle)
            
        self.interior_region = interior
    
    def get_usable_area(self) -> float:
        """Calculate total usable area for parking and driving."""
        if self.interior_region:
            return self.interior_region.area
        return self.boundary.area
    
    def calculate_corner_angle(self, edge1_id: int, edge2_id: int) -> float:
        """Calculate angle between two adjacent edges."""
        if abs(edge1_id - edge2_id) != 1 and not (edge1_id == 0 and edge2_id == len(self.perimeter_edges) - 1):
            return 180.0  # Not adjacent edges
            
        edge1 = self.perimeter_edges[edge1_id]
        edge2 = self.perimeter_edges[edge2_id]
        
        # Calculate angle between edge orientations
        angle_diff = abs(edge2.orientation - edge1.orientation)
        angle_diff = min(angle_diff, 2 * math.pi - angle_diff)  # Take smaller angle
        
        return math.degrees(angle_diff)
    
    def detect_edge_conflicts(self, edge1_id: int, edge2_id: int) -> List[str]:
        """Detect conflicts between perimeter edge configurations."""
        conflicts = []
        
        if abs(edge1_id - edge2_id) == 1 or (edge1_id == 0 and edge2_id == len(self.perimeter_edges) - 1):
            edge1 = self.perimeter_edges[edge1_id]
            edge2 = self.perimeter_edges[edge2_id]
            
            corner_angle = self.calculate_corner_angle(edge1_id, edge2_id)
            
            # Sharp corner conflicts
            if corner_angle < 90:
                if (edge1.configuration == EdgeConfiguration.PERPENDICULAR_PARKING and 
                    edge2.configuration == EdgeConfiguration.PERPENDICULAR_PARKING):
                    conflicts.append("perpendicular_corner_conflict")
            
            # Turning radius conflicts
            if (edge1.configuration != EdgeConfiguration.DRIVING_ONLY and 
                edge2.configuration != EdgeConfiguration.DRIVING_ONLY):
                if corner_angle < 120:  # Insufficient turning radius
                    conflicts.append("turning_radius_conflict")
        
        return conflicts
    
    def validate_perimeter_configuration(self) -> Tuple[bool, List[str]]:
        """Validate current perimeter configuration."""
        issues = []
        
        # Check for entrance accessibility
        has_accessible_entrance = False
        for entrance in self.entrances:
            edge_id = entrance[2]
            edge = self.perimeter_edges[edge_id]
            if edge.configuration != EdgeConfiguration.PARALLEL_PARKING:
                has_accessible_entrance = True
                break
        
        if not has_accessible_entrance:
            issues.append("no_accessible_entrance")
        
        # Check for conflicts between adjacent edges
        for i in range(len(self.perimeter_edges)):
            next_i = (i + 1) % len(self.perimeter_edges)
            conflicts = self.detect_edge_conflicts(i, next_i)
            issues.extend(conflicts)
        
        # Check minimum driving width
        for edge in self.perimeter_edges:
            if edge.configuration != EdgeConfiguration.DRIVING_ONLY:
                if edge.length < self.min_driving_width:
                    issues.append(f"insufficient_width_edge_{edge.edge_id}")
        
        return len(issues) == 0, issues
