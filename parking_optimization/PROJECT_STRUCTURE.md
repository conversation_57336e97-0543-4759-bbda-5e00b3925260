# Parking Lot Optimization Project Structure

This document provides a comprehensive overview of the project structure and file organization.

## Project Overview

The parking lot optimization system implements a hybrid rule-based and quantum annealing approach for optimizing parking lot layouts. The system is organized into logical modules with clear separation of concerns.

## File Structure

```
parking_optimization/
├── Core Modules
│   ├── __init__.py              # Package initialization and exports
│   ├── geometry.py              # Geometric classes and spatial calculations
│   ├── optimization.py          # Quantum annealing optimization engine
│   ├── visualization.py         # Comprehensive visualization system
│   └── test_data.py            # Test scenario generation
│
├── Execution Scripts
│   ├── main.py                 # Main execution script with CLI
│   ├── run_demo.py             # Quick demo script
│   ├── example_usage.py        # Example usage patterns
│   └── setup.py               # Setup and installation script
│
├── Testing and Validation
│   └── test_system.py          # Comprehensive system tests
│
├── Documentation
│   ├── README.md               # Main documentation
│   ├── PROJECT_STRUCTURE.md    # This file
│   └── requirements.txt        # Python dependencies
│
└── Generated Output (created during execution)
    └── optimization_results/   # Results directory
        ├── rectangular_simple/
        ├── rectangular_with_obstacles/
        ├── l_shaped_lot/
        ├── irregular_complex/
        └── large_commercial/
```

## Module Descriptions

### Core Modules

#### `geometry.py`
**Purpose**: Handles all geometric aspects of parking lot design
**Key Classes**:
- `ParkingLot`: Main class representing a parking lot
- `PerimeterEdge`: Represents perimeter edges with configurations
- `Obstacle`: Represents obstacles within the lot
- `EdgeConfiguration`: Enum for edge types

**Key Functions**:
- Perimeter edge creation and management
- Conflict detection between adjacent edges
- Spatial calculations and validations
- Entrance and obstacle management

#### `optimization.py`
**Purpose**: Implements the quantum annealing optimization approach
**Key Classes**:
- `QuantumOptimizer`: Main optimization engine
- `CandidateGenerator`: Generates diverse layout candidates
- `LayoutCandidate`: Represents a candidate solution

**Key Functions**:
- Multi-candidate generation using A* inspired variations
- QUBO formulation for quantum annealing
- Iterative refinement with feedback
- Capacity estimation and smoothness scoring

#### `visualization.py`
**Purpose**: Provides comprehensive visualization capabilities
**Key Classes**:
- `ParkingLotVisualizer`: Main visualization system

**Key Functions**:
- Original lot plotting with obstacles and entrances
- Optimized layout visualization with configurations
- Candidate comparison plots
- Optimization metrics analysis
- Result saving and export

#### `test_data.py`
**Purpose**: Generates test scenarios for system validation
**Key Classes**:
- `TestDataGenerator`: Generates various test scenarios

**Key Functions**:
- 5 predefined test scenarios with varying complexity
- Custom lot generation with specified parameters
- Random obstacle placement
- Scenario information and metadata

### Execution Scripts

#### `main.py`
**Purpose**: Main execution script with command-line interface
**Features**:
- Full optimization demo on all scenarios
- Single scenario demonstration
- Command-line argument processing
- Results saving and summary generation

**Usage**:
```bash
python main.py              # Full demo
python main.py demo [name]  # Single scenario
python main.py list         # List scenarios
```

#### `run_demo.py`
**Purpose**: Quick demo script for easy execution
**Features**:
- Interactive demo with user choices
- Quick demo mode for testing
- No command-line arguments required

#### `example_usage.py`
**Purpose**: Demonstrates various system features
**Examples**:
- Basic optimization workflow
- Custom lot design
- Candidate analysis
- Perimeter configuration
- Batch processing

#### `setup.py`
**Purpose**: System setup and installation verification
**Features**:
- Python version checking
- Dependency installation
- System test execution
- Usage instructions

### Testing and Validation

#### `test_system.py`
**Purpose**: Comprehensive system testing
**Test Categories**:
- Geometry module functionality
- Test data generation
- Candidate generation
- Optimization engine
- Visualization system
- Integration testing

## Data Flow

```
1. Test Data Generation
   TestDataGenerator → ParkingLot instances

2. Candidate Generation
   ParkingLot → CandidateGenerator → LayoutCandidate list

3. Optimization
   LayoutCandidate list → QuantumOptimizer → Best solution

4. Visualization
   ParkingLot + Solution → ParkingLotVisualizer → Plots/Files

5. Results
   All components → Results directory with plots and data
```

## Key Design Patterns

### 1. Modular Architecture
- Clear separation between geometry, optimization, and visualization
- Each module has well-defined responsibilities
- Minimal coupling between modules

### 2. Strategy Pattern
- Multiple candidate generation strategies
- Different optimization approaches
- Configurable visualization options

### 3. Factory Pattern
- TestDataGenerator creates various scenario types
- CandidateGenerator creates different candidate types
- Flexible object creation with parameters

### 4. Observer Pattern
- Optimization progress tracking
- Result collection and analysis
- Feedback-driven improvements

## Configuration and Customization

### Optimization Parameters
Located in `QuantumOptimizer` class:
- `lambda_capacity`: Capacity maximization weight
- `lambda_smoothness`: Road smoothness weight
- `lambda_conflicts`: Conflict penalty weight
- `lambda_constraints`: Hard constraint weight

### Visualization Settings
Located in `ParkingLotVisualizer` class:
- Color schemes for different elements
- Figure sizes and layouts
- Plot styling and formatting

### Geometric Parameters
Located in `ParkingLot` class:
- Minimum driving width
- Parking space dimensions
- Minimum turning radius
- Buffer distances

## Extension Points

### Adding New Test Scenarios
1. Extend `TestDataGenerator.generate_all_test_scenarios()`
2. Add scenario info to `get_test_scenario_info()`
3. Update documentation

### Adding New Optimization Strategies
1. Extend `CandidateGenerator` with new generation methods
2. Add new evaluation metrics to `LayoutCandidate`
3. Update QUBO formulation in `QuantumOptimizer`

### Adding New Visualization Types
1. Add new plot methods to `ParkingLotVisualizer`
2. Extend color schemes and styling options
3. Add new export formats

### Adding New Constraint Types
1. Extend `EdgeConfiguration` enum if needed
2. Add validation methods to `ParkingLot`
3. Update conflict detection logic
4. Modify QUBO formulation

## Performance Considerations

### Computational Complexity
- Candidate generation: O(n) where n = number of candidates
- Optimization: O(n²) for QUBO formulation
- Visualization: O(m) where m = number of geometric elements

### Memory Usage
- Dominated by candidate storage and geometric objects
- Visualization can be memory-intensive for large plots
- Results caching for iterative improvements

### Scalability
- System designed for medium-sized parking lots
- Can handle lots up to ~100m x 100m efficiently
- Larger lots may require parameter tuning

## Dependencies

### Core Dependencies
- `pyqubo`: Quantum annealing formulation
- `numpy`: Numerical computations
- `shapely`: Geometric operations
- `matplotlib`: Visualization

### Analysis Dependencies
- `scipy`: Scientific computing
- `networkx`: Graph algorithms
- `pandas`: Data analysis
- `seaborn`: Statistical visualization

## Future Development

### Planned Enhancements
1. Full PyQUBO quantum annealing integration
2. Multi-level parking structure support
3. Real-time optimization capabilities
4. Traffic simulation integration
5. Cost optimization features

### Research Directions
1. Machine learning for candidate generation
2. Multi-objective optimization
3. Dynamic constraint handling
4. Parallel processing optimization
5. Cloud-based quantum computing integration

This project structure provides a solid foundation for parking lot optimization research and practical applications while maintaining flexibility for future enhancements and extensions.
