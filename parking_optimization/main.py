"""
Main execution script for parking lot optimization demonstration.

This script demonstrates the complete parking lot optimization workflow
including test data generation, optimization, and visualization.
"""

import os
import sys
import time
from typing import List, <PERSON><PERSON>

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from geometry import ParkingLot
from optimization import QuantumOptimizer, LayoutCandidate
from visualization import ParkingLotVisualizer
from test_data import TestDataGenerator


def run_optimization_demo():
    """Run complete optimization demonstration on all test scenarios."""
    print("=" * 60)
    print("PARKING LOT OPTIMIZATION DEMONSTRATION")
    print("Hybrid Rule-Based and Quantum Annealing Approach")
    print("=" * 60)
    
    # Initialize components
    test_generator = TestDataGenerator(seed=42)
    visualizer = ParkingLotVisualizer()
    
    # Generate test scenarios
    print("\nGenerating test scenarios...")
    scenarios = test_generator.generate_all_test_scenarios()
    
    # Create results directory
    results_dir = "optimization_results"
    os.makedirs(results_dir, exist_ok=True)
    
    # Process each scenario
    for scenario_name, parking_lot in scenarios:
        print(f"\n{'-' * 40}")
        print(f"Processing: {scenario_name}")
        print(f"{'-' * 40}")
        
        # Create scenario-specific directory
        scenario_dir = os.path.join(results_dir, scenario_name)
        os.makedirs(scenario_dir, exist_ok=True)
        
        # Run optimization
        start_time = time.time()
        solution, candidates = optimize_parking_lot(parking_lot, scenario_name)
        optimization_time = time.time() - start_time
        
        # Generate visualizations
        print("Generating visualizations...")
        visualizer.save_results(parking_lot, solution, candidates, scenario_dir)
        
        # Print results summary
        print_results_summary(scenario_name, solution, candidates, optimization_time)
        
        # Save detailed results
        save_detailed_results(scenario_dir, parking_lot, solution, candidates)
    
    print(f"\n{'=' * 60}")
    print("OPTIMIZATION COMPLETE")
    print(f"Results saved to: {results_dir}/")
    print(f"{'=' * 60}")


def optimize_parking_lot(parking_lot: ParkingLot, scenario_name: str) -> Tuple[LayoutCandidate, List[LayoutCandidate]]:
    """Optimize a single parking lot scenario."""
    print(f"Initializing optimizer for {scenario_name}...")
    
    # Initialize optimizer
    optimizer = QuantumOptimizer(parking_lot)
    
    # Set optimization parameters based on scenario complexity
    if "simple" in scenario_name:
        num_candidates = 15
        max_iterations = 3
    elif "complex" in scenario_name or "large" in scenario_name:
        num_candidates = 30
        max_iterations = 5
    else:
        num_candidates = 20
        max_iterations = 4
    
    print(f"Running optimization with {num_candidates} candidates, {max_iterations} iterations...")
    
    # Run optimization
    solution = optimizer.optimize(num_candidates=num_candidates, max_iterations=max_iterations)
    
    # Get all evaluated candidates for analysis
    candidates = optimizer.candidate_generator.generate_perimeter_candidates(num_candidates)
    optimizer._evaluate_candidates(candidates)
    
    return solution, candidates


def print_results_summary(scenario_name: str, solution: LayoutCandidate, 
                         candidates: List[LayoutCandidate], optimization_time: float):
    """Print optimization results summary."""
    print(f"\nResults for {scenario_name}:")
    print(f"  Optimization time: {optimization_time:.2f} seconds")
    print(f"  Best solution ID: {solution.candidate_id}")
    print(f"  Total score: {solution.total_score:.2f}")
    print(f"  Estimated capacity: {solution.estimated_capacity} spaces")
    print(f"  Smoothness score: {solution.smoothness_score:.2f}")
    
    # Calculate statistics
    scores = [c.total_score for c in candidates]
    capacities = [c.estimated_capacity for c in candidates]
    
    print(f"  Candidate statistics:")
    print(f"    Score range: {min(scores):.1f} - {max(scores):.1f}")
    print(f"    Capacity range: {min(capacities)} - {max(capacities)} spaces")
    print(f"    Improvement over average: {((solution.total_score - sum(scores)/len(scores)) / (sum(scores)/len(scores)) * 100):.1f}%")


def save_detailed_results(output_dir: str, parking_lot: ParkingLot, 
                         solution: LayoutCandidate, candidates: List[LayoutCandidate]):
    """Save detailed results to files."""
    import json
    
    # Prepare data for JSON serialization
    results_data = {
        "scenario_info": {
            "boundary_area": parking_lot.boundary.area,
            "num_obstacles": len(parking_lot.obstacles),
            "num_entrances": len(parking_lot.entrances),
            "perimeter_length": sum(edge.length for edge in parking_lot.perimeter_edges)
        },
        "optimization_results": {
            "best_solution": {
                "candidate_id": solution.candidate_id,
                "total_score": solution.total_score,
                "estimated_capacity": solution.estimated_capacity,
                "smoothness_score": solution.smoothness_score,
                "perimeter_config": {str(k): v.value for k, v in solution.perimeter_config.items()}
            },
            "all_candidates": [
                {
                    "candidate_id": c.candidate_id,
                    "total_score": c.total_score,
                    "estimated_capacity": c.estimated_capacity,
                    "smoothness_score": c.smoothness_score
                }
                for c in candidates
            ]
        }
    }
    
    # Save to JSON file
    with open(os.path.join(output_dir, "results.json"), 'w') as f:
        json.dump(results_data, f, indent=2)
    
    # Save configuration details
    config_text = format_configuration_details(parking_lot, solution)
    with open(os.path.join(output_dir, "configuration.txt"), 'w') as f:
        f.write(config_text)


def format_configuration_details(parking_lot: ParkingLot, solution: LayoutCandidate) -> str:
    """Format detailed configuration information."""
    lines = []
    lines.append("PARKING LOT OPTIMIZATION CONFIGURATION")
    lines.append("=" * 50)
    lines.append("")
    
    # Lot information
    lines.append("LOT INFORMATION:")
    lines.append(f"  Total area: {parking_lot.boundary.area:.1f} m²")
    lines.append(f"  Perimeter length: {sum(edge.length for edge in parking_lot.perimeter_edges):.1f} m")
    lines.append(f"  Number of obstacles: {len(parking_lot.obstacles)}")
    lines.append(f"  Number of entrances: {len(parking_lot.entrances)}")
    lines.append("")
    
    # Perimeter configuration
    lines.append("PERIMETER CONFIGURATION:")
    for edge_id, config in solution.perimeter_config.items():
        edge = parking_lot.perimeter_edges[edge_id]
        lines.append(f"  Edge {edge_id}: {config.value} (length: {edge.length:.1f}m)")
    lines.append("")
    
    # Optimization metrics
    lines.append("OPTIMIZATION METRICS:")
    lines.append(f"  Total score: {solution.total_score:.2f}")
    lines.append(f"  Estimated capacity: {solution.estimated_capacity} spaces")
    lines.append(f"  Smoothness score: {solution.smoothness_score:.2f}")
    lines.append("")
    
    # Entrance information
    lines.append("ENTRANCE DETAILS:")
    for i, entrance in enumerate(parking_lot.entrances):
        lines.append(f"  Entrance {i+1}: ({entrance[0]:.1f}, {entrance[1]:.1f}) on edge {entrance[2]}")
    
    return "\n".join(lines)


def run_single_scenario_demo(scenario_name: str = "rectangular_simple"):
    """Run optimization on a single scenario for quick testing."""
    print(f"Running single scenario demo: {scenario_name}")
    
    # Generate test data
    test_generator = TestDataGenerator(seed=42)
    scenarios = test_generator.generate_all_test_scenarios()
    
    # Find requested scenario
    parking_lot = None
    for name, lot in scenarios:
        if name == scenario_name:
            parking_lot = lot
            break
    
    if parking_lot is None:
        print(f"Scenario '{scenario_name}' not found!")
        return
    
    # Run optimization
    solution, candidates = optimize_parking_lot(parking_lot, scenario_name)
    
    # Create visualizations
    visualizer = ParkingLotVisualizer()
    
    # Show plots
    import matplotlib.pyplot as plt
    
    fig1 = visualizer.plot_original_lot(parking_lot, f"Original: {scenario_name}")
    fig2 = visualizer.plot_optimized_layout(parking_lot, solution, f"Optimized: {scenario_name}")
    fig3 = visualizer.plot_comparison(parking_lot, candidates[:6], f"Top Candidates: {scenario_name}")
    fig4 = visualizer.plot_optimization_metrics(candidates)
    
    plt.show()
    
    # Print summary
    print_results_summary(scenario_name, solution, candidates, 0.0)


if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            # Run single scenario demo
            scenario = sys.argv[2] if len(sys.argv) > 2 else "rectangular_simple"
            run_single_scenario_demo(scenario)
        elif sys.argv[1] == "list":
            # List available scenarios
            test_generator = TestDataGenerator()
            scenarios_info = test_generator.get_test_scenario_info()
            print("Available test scenarios:")
            for info in scenarios_info:
                print(f"  {info['name']}: {info['description']}")
        else:
            print("Usage:")
            print("  python main.py              - Run full optimization demo")
            print("  python main.py demo [name]  - Run single scenario demo")
            print("  python main.py list         - List available scenarios")
    else:
        # Run full demonstration
        run_optimization_demo()
