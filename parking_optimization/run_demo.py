#!/usr/bin/env python3
"""
Quick demo script for parking lot optimization.

This script provides a simple way to run the parking lot optimization
demonstration without command line arguments.
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import run_single_scenario_demo, run_optimization_demo
from test_data import TestDataGenerator


def interactive_demo():
    """Run interactive demo with user choices."""
    print("=" * 60)
    print("PARKING LOT OPTIMIZATION - INTERACTIVE DEMO")
    print("=" * 60)
    print()
    
    # Show available scenarios
    generator = TestDataGenerator()
    scenarios_info = generator.get_test_scenario_info()
    
    print("Available test scenarios:")
    for i, info in enumerate(scenarios_info, 1):
        print(f"  {i}. {info['name']}")
        print(f"     {info['description']}")
        print(f"     Complexity: {info['complexity']}")
        print(f"     Features: {', '.join(info['features'])}")
        print()
    
    print("Demo options:")
    print("  A. Run single scenario demo")
    print("  B. Run full optimization demo (all scenarios)")
    print("  C. Exit")
    print()
    
    while True:
        choice = input("Select option (A/B/C): ").strip().upper()
        
        if choice == 'A':
            # Single scenario demo
            print("\nSelect scenario:")
            for i, info in enumerate(scenarios_info, 1):
                print(f"  {i}. {info['name']}")
            
            try:
                scenario_num = int(input("Enter scenario number (1-5): "))
                if 1 <= scenario_num <= len(scenarios_info):
                    scenario_name = scenarios_info[scenario_num - 1]['name']
                    print(f"\nRunning demo for: {scenario_name}")
                    run_single_scenario_demo(scenario_name)
                    break
                else:
                    print("Invalid scenario number!")
            except ValueError:
                print("Please enter a valid number!")
                
        elif choice == 'B':
            # Full demo
            print("\nRunning full optimization demo...")
            print("This will process all scenarios and save results.")
            confirm = input("Continue? (y/n): ").strip().lower()
            if confirm == 'y':
                run_optimization_demo()
                break
            else:
                print("Demo cancelled.")
                
        elif choice == 'C':
            print("Goodbye!")
            break
            
        else:
            print("Invalid choice! Please select A, B, or C.")


def quick_demo():
    """Run a quick demo with the simplest scenario."""
    print("QUICK DEMO - Running rectangular_simple scenario...")
    print("-" * 50)
    
    try:
        run_single_scenario_demo("rectangular_simple")
        print("\nQuick demo completed successfully!")
        print("For more options, run: python run_demo.py interactive")
    except Exception as e:
        print(f"Demo failed with error: {e}")
        print("Please check that all dependencies are installed.")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_demo()
    else:
        quick_demo()
