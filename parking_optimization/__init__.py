"""
Parking Lot Optimization using Hybrid Rule-Based and Quantum Annealing Approach

This package implements a comprehensive parking lot optimization system that combines:
1. Rule-based perimeter configuration
2. Quantum annealing optimization for interior layout
3. Multi-candidate generation strategy adapted from PCB routing methodology

Author: AI Assistant
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

from .geometry import Parking<PERSON>ot, PerimeterEdge, Obstacle
from .optimization import QuantumOptimizer, CandidateGenerator
from .visualization import ParkingLotVisualizer
from .test_data import TestDataGenerator

__all__ = [
    'ParkingLot',
    'PerimeterEdge', 
    'Obstacle',
    'QuantumOptimizer',
    'CandidateGenerator',
    'ParkingLotVisualizer',
    'TestDataGenerator'
]
