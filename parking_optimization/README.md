# Parking Lot Optimization using Hybrid Rule-Based and Quantum Annealing Approach

This project implements a comprehensive parking lot optimization system that combines rule-based perimeter configuration with quantum annealing optimization for interior layout design. The approach is adapted from PCB routing methodology to solve spatial optimization problems in parking lot design.

## Features

- **Hybrid Optimization**: Combines rule-based hard constraints with quantum annealing soft optimization
- **Multi-Candidate Generation**: A* inspired candidate generation strategy for diverse solution exploration
- **Comprehensive Visualization**: Complete visualization system for layouts, metrics, and comparisons
- **Multiple Test Scenarios**: 5 different test scenarios with varying complexity levels
- **Road Smoothness Optimization**: QUBO formulation includes road smoothness constraints
- **Conflict Resolution**: Automatic detection and resolution of perimeter edge conflicts

## Project Structure

```
parking_optimization/
├── __init__.py              # Package initialization
├── geometry.py              # Geometric classes and spatial calculations
├── optimization.py          # Quantum annealing optimization engine
├── visualization.py         # Comprehensive visualization system
├── test_data.py            # Test scenario generation
├── main.py                 # Main execution script
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

## Installation

1. Clone or download the project files
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Quick Start

Run a single scenario demonstration:
```bash
python main.py demo rectangular_simple
```

### Full Demonstration

Run optimization on all test scenarios:
```bash
python main.py
```

### List Available Scenarios

```bash
python main.py list
```

### Available Test Scenarios

1. **rectangular_simple**: Simple 50x30m rectangular lot with single entrance
2. **rectangular_with_obstacles**: 60x40m rectangular lot with random obstacles  
3. **l_shaped_lot**: L-shaped lot with two entrances and obstacles
4. **irregular_complex**: Irregular shaped lot with multiple entrances and obstacles
5. **large_commercial**: Large 100x80m commercial lot with central building

## Key Components

### Geometry Module (`geometry.py`)

- **ParkingLot**: Main class representing parking lot with boundary, obstacles, entrances
- **PerimeterEdge**: Represents perimeter edges with configuration options
- **Obstacle**: Represents obstacles within the parking lot
- **EdgeConfiguration**: Enum for edge types (driving-only, perpendicular parking, parallel parking)

### Optimization Module (`optimization.py`)

- **QuantumOptimizer**: Main optimization engine using quantum annealing approach
- **CandidateGenerator**: Generates diverse layout candidates using A* inspired variations
- **LayoutCandidate**: Represents a candidate solution with metrics

### Visualization Module (`visualization.py`)

- **ParkingLotVisualizer**: Comprehensive visualization system
- Plots original lots, optimized layouts, candidate comparisons, and metrics analysis
- Saves results as high-quality images and data files

### Test Data Module (`test_data.py`)

- **TestDataGenerator**: Generates various test scenarios
- Creates lots with different shapes, obstacles, and entrance configurations
- Supports custom lot generation

## Algorithm Overview

### 1. Rule-Based Perimeter Configuration

- Classifies perimeter edges into three types:
  - Driving-only lanes
  - Perpendicular parking (90° to edge)
  - Parallel parking (0° along edge)
- Detects and resolves conflicts between adjacent edges
- Ensures entrance accessibility and traffic flow

### 2. Multi-Candidate Generation

- Generates diverse perimeter configuration candidates
- Uses A* inspired variations for exploration
- Creates base configurations (all-driving, max-parking, balanced, entrance-optimized)
- Applies random perturbations for diversity

### 3. Quantum Annealing Optimization

- Formulates QUBO (Quadratic Unconstrained Binary Optimization) problem
- Includes terms for:
  - Parking capacity maximization
  - Road smoothness optimization
  - Conflict minimization
  - Constraint satisfaction
- Uses iterative refinement with feedback

### 4. Interior Capacity Estimation

- Fast estimation function for interior parking capacity
- Considers usable area, required driving lanes, and space efficiency
- Accounts for perimeter parking contributions

## Output

The system generates:

1. **Visualizations**:
   - Original parking lot with obstacles and entrances
   - Optimized layout with perimeter configurations
   - Candidate comparison plots
   - Optimization metrics analysis

2. **Data Files**:
   - JSON results with detailed metrics
   - Configuration text files
   - High-resolution plot images

3. **Console Output**:
   - Optimization progress
   - Results summary with statistics
   - Performance metrics

## Customization

### Adding New Test Scenarios

```python
from test_data import TestDataGenerator

generator = TestDataGenerator()
custom_lot = generator.generate_custom_lot(
    vertices=[(0,0), (30,0), (30,20), (0,20)],
    entrance_specs=[(0, 0.5)],  # Edge 0, position 50%
    obstacle_specs=[
        ([(10,10), (15,10), (15,15), (10,15)], "building")
    ]
)
```

### Adjusting Optimization Parameters

```python
optimizer = QuantumOptimizer(parking_lot)
optimizer.lambda_capacity = 1.5      # Increase capacity weight
optimizer.lambda_smoothness = 0.5    # Increase smoothness weight
optimizer.lambda_conflicts = 3.0     # Increase conflict penalty
```

## Technical Details

### QUBO Formulation

The optimization problem is formulated as:

```
minimize: λ₁(capacity_term) + λ₂(smoothness_term) + λ₃(conflict_term) + λ₄(constraint_term)
```

Where:
- **capacity_term**: Maximizes parking space count (negative for minimization)
- **smoothness_term**: Penalizes sharp turns and rough road transitions
- **conflict_term**: Penalizes configuration conflicts
- **constraint_term**: Enforces hard constraints

### Road Smoothness Metrics

- **Curvature penalty**: Exponential penalty for sharp curves
- **Direction change penalty**: Linear penalty for frequent direction changes  
- **Turn angle penalty**: Quadratic penalty for sharp turns at intersections

## Future Enhancements

1. **Full PyQUBO Integration**: Complete quantum annealing implementation
2. **Multi-Level Optimization**: Support for multi-story parking structures
3. **Real-Time Adaptation**: Dynamic reconfiguration for changing conditions
4. **Traffic Simulation**: Integration with traffic flow simulation
5. **Cost Optimization**: Include construction and maintenance costs

## License

This project is provided as an educational demonstration of hybrid optimization approaches for spatial planning problems.

## References

- Original parking lot optimization research: arXiv:2506.09961v1
- PCB routing quantum annealing methodology
- PyQUBO documentation: https://pyqubo.readthedocs.io/
