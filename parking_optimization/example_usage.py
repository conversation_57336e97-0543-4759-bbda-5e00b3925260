"""
Example usage script demonstrating various features of the parking lot optimization system.

This script shows how to use different components of the system for custom applications.
"""

import matplotlib.pyplot as plt
from geometry import ParkingLot, Obstacle, EdgeConfiguration
from optimization import QuantumOptimizer, CandidateGenerator
from visualization import Parking<PERSON>otVisualizer
from test_data import TestDataGenerator
from shapely.geometry import <PERSON>ygon


def example_1_basic_optimization():
    """Example 1: Basic optimization workflow."""
    print("Example 1: Basic Optimization Workflow")
    print("-" * 40)
    
    # Create a simple rectangular parking lot
    boundary = Polygon([(0, 0), (40, 0), (40, 25), (0, 25)])
    parking_lot = ParkingLot(boundary)
    
    # Add entrance
    parking_lot.add_entrance(edge_id=0, position_ratio=0.3)
    
    # Add some obstacles
    obstacle1 = Obstacle(Polygon([(15, 10), (20, 10), (20, 15), (15, 15)]), "building")
    obstacle2 = Obstacle(Polygon([(5, 5), (8, 5), (8, 8), (5, 8)]), "utility")
    
    parking_lot.add_obstacle(obstacle1)
    parking_lot.add_obstacle(obstacle2)
    
    # Run optimization
    optimizer = QuantumOptimizer(parking_lot)
    solution = optimizer.optimize(num_candidates=15, max_iterations=3)
    
    # Visualize results
    visualizer = ParkingLotVisualizer()
    fig1 = visualizer.plot_original_lot(parking_lot, "Example 1: Original Lot")
    fig2 = visualizer.plot_optimized_layout(parking_lot, solution, "Example 1: Optimized Layout")
    
    plt.show()
    
    print(f"Optimization complete!")
    print(f"Best solution score: {solution.total_score:.2f}")
    print(f"Estimated capacity: {solution.estimated_capacity} spaces")
    print()


def example_2_custom_lot_design():
    """Example 2: Custom lot design with specific requirements."""
    print("Example 2: Custom Lot Design")
    print("-" * 40)
    
    # Create custom irregular lot
    vertices = [
        (0, 0), (30, 0), (35, 10), (40, 20), (35, 30), 
        (25, 35), (15, 30), (5, 25), (0, 15)
    ]
    
    generator = TestDataGenerator()
    parking_lot = generator.generate_custom_lot(
        vertices=vertices,
        entrance_specs=[(0, 0.4), (3, 0.6)],  # Two entrances
        obstacle_specs=[
            ([(20, 15), (25, 15), (25, 20), (20, 20)], "central_building"),
            ([(10, 10), (12, 10), (12, 12), (10, 12)], "tree"),
            ([(30, 25), (32, 25), (32, 27), (30, 27)], "utility")
        ]
    )
    
    # Customize optimization parameters
    optimizer = QuantumOptimizer(parking_lot)
    optimizer.lambda_capacity = 1.5      # Prioritize capacity
    optimizer.lambda_smoothness = 0.8    # High smoothness requirement
    optimizer.lambda_conflicts = 2.5     # Strict conflict avoidance
    
    # Run optimization
    solution = optimizer.optimize(num_candidates=25, max_iterations=4)
    
    # Generate comprehensive analysis
    candidates = optimizer.candidate_generator.generate_perimeter_candidates(25)
    optimizer._evaluate_candidates(candidates)
    
    visualizer = ParkingLotVisualizer()
    fig1 = visualizer.plot_original_lot(parking_lot, "Example 2: Custom Irregular Lot")
    fig2 = visualizer.plot_optimized_layout(parking_lot, solution, "Example 2: Optimized Custom Lot")
    fig3 = visualizer.plot_comparison(parking_lot, candidates[:6], "Example 2: Top Candidates")
    fig4 = visualizer.plot_optimization_metrics(candidates)
    
    plt.show()
    
    print(f"Custom lot optimization complete!")
    print(f"Lot area: {parking_lot.boundary.area:.1f} m²")
    print(f"Number of obstacles: {len(parking_lot.obstacles)}")
    print(f"Best solution score: {solution.total_score:.2f}")
    print()


def example_3_candidate_analysis():
    """Example 3: Detailed candidate generation and analysis."""
    print("Example 3: Candidate Analysis")
    print("-" * 40)
    
    # Use L-shaped lot from test data
    generator = TestDataGenerator()
    scenarios = generator.generate_all_test_scenarios()
    parking_lot = next(lot for name, lot in scenarios if name == "l_shaped_lot")
    
    # Generate and analyze candidates
    candidate_gen = CandidateGenerator(parking_lot)
    candidates = candidate_gen.generate_perimeter_candidates(30)
    
    # Evaluate all candidates
    optimizer = QuantumOptimizer(parking_lot)
    optimizer._evaluate_candidates(candidates)
    
    # Analyze candidate diversity
    print("Candidate Analysis:")
    print(f"Total candidates generated: {len(candidates)}")
    
    # Group by configuration patterns
    config_patterns = {}
    for candidate in candidates:
        pattern = tuple(sorted(candidate.perimeter_config.values()))
        if pattern not in config_patterns:
            config_patterns[pattern] = []
        config_patterns[pattern].append(candidate)
    
    print(f"Unique configuration patterns: {len(config_patterns)}")
    
    # Show top candidates
    sorted_candidates = sorted(candidates, key=lambda c: c.total_score, reverse=True)
    print("\nTop 5 candidates:")
    for i, candidate in enumerate(sorted_candidates[:5]):
        print(f"  {i+1}. ID {candidate.candidate_id}: Score {candidate.total_score:.2f}, "
              f"Capacity {candidate.estimated_capacity}, Smoothness {candidate.smoothness_score:.2f}")
    
    # Visualize analysis
    visualizer = ParkingLotVisualizer()
    fig1 = visualizer.plot_comparison(parking_lot, sorted_candidates[:6], "Example 3: Top Candidates")
    fig2 = visualizer.plot_optimization_metrics(candidates)
    
    plt.show()
    print()


def example_4_perimeter_configuration():
    """Example 4: Manual perimeter configuration and validation."""
    print("Example 4: Perimeter Configuration")
    print("-" * 40)
    
    # Create rectangular lot
    boundary = Polygon([(0, 0), (50, 0), (50, 30), (0, 30)])
    parking_lot = ParkingLot(boundary)
    parking_lot.add_entrance(edge_id=0, position_ratio=0.2)
    
    # Test different perimeter configurations
    configurations = [
        {0: EdgeConfiguration.DRIVING_ONLY, 1: EdgeConfiguration.PERPENDICULAR_PARKING,
         2: EdgeConfiguration.DRIVING_ONLY, 3: EdgeConfiguration.PERPENDICULAR_PARKING},
        
        {0: EdgeConfiguration.DRIVING_ONLY, 1: EdgeConfiguration.PARALLEL_PARKING,
         2: EdgeConfiguration.PARALLEL_PARKING, 3: EdgeConfiguration.PARALLEL_PARKING},
        
        {0: EdgeConfiguration.DRIVING_ONLY, 1: EdgeConfiguration.PERPENDICULAR_PARKING,
         2: EdgeConfiguration.PERPENDICULAR_PARKING, 3: EdgeConfiguration.PERPENDICULAR_PARKING}
    ]
    
    visualizer = ParkingLotVisualizer()
    
    for i, config in enumerate(configurations):
        # Create candidate with this configuration
        from optimization import LayoutCandidate
        candidate = LayoutCandidate(i)
        candidate.set_perimeter_configuration(config)
        candidate.calculate_smoothness_score(parking_lot)
        
        # Estimate capacity
        candidate_gen = CandidateGenerator(parking_lot)
        candidate.estimated_capacity = candidate_gen.estimate_interior_capacity(config)
        candidate.total_score = candidate.estimated_capacity + candidate.smoothness_score
        
        # Validate configuration
        is_valid, issues = parking_lot.validate_perimeter_configuration()
        
        print(f"Configuration {i+1}:")
        print(f"  Valid: {is_valid}")
        if issues:
            print(f"  Issues: {', '.join(issues)}")
        print(f"  Estimated capacity: {candidate.estimated_capacity}")
        print(f"  Smoothness score: {candidate.smoothness_score:.2f}")
        print()
        
        # Visualize configuration
        fig = visualizer.plot_optimized_layout(parking_lot, candidate, 
                                             f"Example 4: Configuration {i+1}")
        plt.show()


def example_5_batch_processing():
    """Example 5: Batch processing of multiple scenarios."""
    print("Example 5: Batch Processing")
    print("-" * 40)
    
    # Generate all test scenarios
    generator = TestDataGenerator()
    scenarios = generator.generate_all_test_scenarios()
    
    results = []
    
    for scenario_name, parking_lot in scenarios:
        print(f"Processing {scenario_name}...")
        
        # Quick optimization
        optimizer = QuantumOptimizer(parking_lot)
        solution = optimizer.optimize(num_candidates=10, max_iterations=2)
        
        # Store results
        results.append({
            'scenario': scenario_name,
            'area': parking_lot.boundary.area,
            'obstacles': len(parking_lot.obstacles),
            'entrances': len(parking_lot.entrances),
            'score': solution.total_score,
            'capacity': solution.estimated_capacity,
            'smoothness': solution.smoothness_score
        })
    
    # Print summary
    print("\nBatch Processing Results:")
    print("-" * 60)
    print(f"{'Scenario':<20} {'Area':<8} {'Obs':<4} {'Ent':<4} {'Score':<8} {'Capacity':<8} {'Smooth':<8}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['scenario']:<20} {result['area']:<8.0f} {result['obstacles']:<4} "
              f"{result['entrances']:<4} {result['score']:<8.1f} {result['capacity']:<8} "
              f"{result['smoothness']:<8.1f}")
    
    # Find best performing scenario
    best_scenario = max(results, key=lambda r: r['score'])
    print(f"\nBest performing scenario: {best_scenario['scenario']}")
    print(f"Score: {best_scenario['score']:.1f}, Capacity: {best_scenario['capacity']}")
    print()


if __name__ == "__main__":
    print("PARKING LOT OPTIMIZATION - EXAMPLE USAGE")
    print("=" * 50)
    print()
    
    # Run all examples
    examples = [
        example_1_basic_optimization,
        example_2_custom_lot_design,
        example_3_candidate_analysis,
        example_4_perimeter_configuration,
        example_5_batch_processing
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
        except Exception as e:
            print(f"Error in example {i}: {e}")
            print()
        
        if i < len(examples):
            input("Press Enter to continue to next example...")
            print()
    
    print("All examples completed!")
